-- Migration to fix student check constraints to allow empty strings
-- Run this script to update existing database constraints

-- Drop existing check constraints
ALTER TABLE students DROP CONSTRAINT IF EXISTS students_class_check;
ALTER TABLE students DROP CONSTRAINT IF EXISTS students_stream_check;

-- Add new check constraints that allow empty strings and NULL values
ALTER TABLE students ADD CONSTRAINT students_class_check CHECK (class IS NULL OR class = '' OR class IN ('9th', '10th', '11th', '12th', 'dropper'));
ALTER TABLE students ADD CONSTRAINT students_stream_check CHECK (stream IS NULL OR stream = '' OR stream IN ('IIT-JEE', 'NEET'));

-- Verify the changes
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'students'::regclass 
AND conname IN ('students_class_check', 'students_stream_check');
