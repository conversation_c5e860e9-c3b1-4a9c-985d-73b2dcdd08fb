package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateTopic(ctx context.Context, topic *models.Topic, chapterName string) (*models.Topic, error) {
	start := time.Now()
	slog.Info("Creating topic", "topic_name", topic.Name, "chapter_name", chapterName)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for topic creation",
			"topic_name", topic.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	topic.ChapterID = chapter.ID

	res := p.db.Create(topic)
	if res.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to create topic",
			"topic_name", topic.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	// Load the topic with chapter association
	if err := p.db.Preload("Chapter").First(topic, topic.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load topic with chapter association",
			"topic_name", topic.Name,
			"topic_id", topic.ID,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to load topic with chapter: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Topic created successfully",
		"topic_name", topic.Name,
		"topic_id", topic.ID,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return topic, nil
}

func (p *DbPlugin) CreateQuestion(ctx context.Context, question *models.Question, topicName, difficultyName string, options []models.OptionForCreate) (*models.Question, error) {
	start := time.Now()
	slog.Info("Creating question",
		"question_text", question.Text,
		"topic_name", topicName,
		"difficulty_name", difficultyName,
	)

	// Find the topic by name
	var topic models.Topic
	if err := p.db.Where("name = ?", topicName).First(&topic).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Topic not found for question creation",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("topic '%s' not found: %w", topicName, err)
	}

	// Find the difficulty by name
	var difficulty models.Difficulty
	if err := p.db.Where("name = ?", difficultyName).First(&difficulty).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Difficulty not found for question creation",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("difficulty '%s' not found: %w", difficultyName, err)
	}

	// Set the IDs
	question.TopicID = topic.ID
	question.DifficultyID = difficulty.ID

	slog.Debug("Creating question with associations",
		"topic_id", topic.ID,
		"topic_name", topicName,
		"difficulty_id", difficulty.ID,
		"difficulty_name", difficultyName,
	)

	// Start a transaction to ensure atomicity
	tx := p.db.Begin()
	if tx.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to start transaction for question creation",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", tx.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, tx.Error
	}

	// Create the question
	res := tx.Create(question)
	if res.Error != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create question",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"topic_id", topic.ID,
			"difficulty_id", difficulty.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	// Create options if provided
	if len(options) > 0 {
		slog.Debug("Creating options for question",
			"question_id", question.ID,
			"option_count", len(options),
		)

		for i, optionInput := range options {
			option := models.Option{
				QuestionID:     question.ID,
				OptionText:     optionInput.OptionText,
				OptionImageURL: optionInput.OptionImageURL,
				IsCorrect:      optionInput.IsCorrect,
			}

			if err := tx.Create(&option).Error; err != nil {
				tx.Rollback()
				duration := time.Since(start)
				slog.Error("Failed to create option",
					"question_id", question.ID,
					"option_index", i,
					"option_text", optionInput.OptionText,
					"error", err.Error(),
					"duration_ms", duration.Milliseconds(),
				)
				return nil, fmt.Errorf("failed to create option %d: %w", i, err)
			}
		}

		slog.Debug("Options created successfully",
			"question_id", question.ID,
			"option_count", len(options),
		)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit transaction",
			"question_id", question.ID,
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Load the question with all associations
	if err := p.db.Preload("Topic").Preload("Difficulty").Preload("Options").First(question, question.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load question with associations",
			"question_id", question.ID,
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to load question with associations: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Question created successfully",
		"question_id", question.ID,
		"question_text", question.Text,
		"topic_name", topicName,
		"topic_id", topic.ID,
		"difficulty_name", difficultyName,
		"difficulty_id", difficulty.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return question, nil
}

func (p *DbPlugin) GetQuestions(ctx context.Context, topicName,
	difficulty string) ([]models.Question, error) {
	start := time.Now()
	slog.Debug("Retrieving questions", "topic_name", topicName, "difficulty", difficulty)

	var questions []models.Question

	err := p.db.Preload("Topic").
		Preload("Difficulty").
		Joins("JOIN topics ON topics.id = questions.topic_id").
		Joins("JOIN difficulties ON difficulties.id = questions.difficulty_id").
		Where("topics.name = ? AND difficulties.name = ?", topicName, difficulty).
		Find(&questions).Error

	duration := time.Since(start)

	if err != nil {
		slog.Error("Failed to retrieve questions",
			"topic_name", topicName,
			"difficulty", difficulty,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to get questions: %w", err)
	}

	slog.Info("Questions retrieved successfully",
		"topic_name", topicName,
		"difficulty", difficulty,
		"question_count", len(questions),
		"duration_ms", duration.Milliseconds(),
	)
	return questions, nil
}

// GetTopics retrieves topics for a given chapter ID, or all topics if chapterID is nil
func (p *DbPlugin) GetTopics(ctx context.Context, chapterID *uint) ([]models.Topic, error) {
	start := time.Now()

	var topics []models.Topic
	query := p.db.Preload("Chapter")

	if chapterID != nil {
		slog.Debug("Retrieving topics for chapter", "chapter_id", *chapterID)
		query = query.Where("chapter_id = ?", *chapterID)
	} else {
		slog.Debug("Retrieving all topics")
	}

	if err := query.Find(&topics).Error; err != nil {
		duration := time.Since(start)
		if chapterID != nil {
			slog.Error("Failed to retrieve topics for chapter",
				"chapter_id", *chapterID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
		} else {
			slog.Error("Failed to retrieve all topics",
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
		}
		return nil, err
	}

	duration := time.Since(start)
	if chapterID != nil {
		slog.Info("Topics retrieved successfully",
			"chapter_id", *chapterID,
			"topic_count", len(topics),
			"duration_ms", duration.Milliseconds(),
		)
	} else {
		slog.Info("All topics retrieved successfully",
			"topic_count", len(topics),
			"duration_ms", duration.Milliseconds(),
		)
	}
	return topics, nil
}
